import * as aiService from "../services/ai.service.js";

export const generateResult = async(req,res)=>{
    try {
        // Get prompt from URL parameter if it exists, otherwise from body
        const prompt = req.params.prompt || req.body.prompt;

        if (!prompt) {
            return res.status(400).json({message: "Prompt is required"});
        }

        const result = await aiService.generateResult(prompt);
        res.status(200).json({result});
    } catch (error) {
        res.status(500).json({message: error.message});
    }
}