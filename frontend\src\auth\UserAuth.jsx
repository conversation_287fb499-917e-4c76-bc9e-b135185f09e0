import {useContext, useState} from 'react'
import UserContext from '../context/user.context'
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

function UserAuth({children}) {
    const {user} = useContext(UserContext);
    const [loading, setLoading] = useState(true);
    const token = localStorage.getItem('token');
    const navigate = useNavigate();

   

    if(loading){
        <div>Loading...</div>
    }

    useEffect(() => {
        if(user){
        setLoading(false);
    }
        if(!token){
            navigate('/login');
        }
        if(!user){
            navigate('/login');
        }
    }, []);

  return (
    <>
      {children}
    </>
  )
}

export default UserAuth
